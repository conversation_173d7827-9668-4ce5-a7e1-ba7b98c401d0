name: Performance Testing

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run performance tests daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of performance test to run'
        required: true
        default: 'regression'
        type: choice
        options:
          - regression
          - load
          - full
      environment:
        description: 'Environment to test against'
        required: true
        default: 'test'
        type: choice
        options:
          - test
          - staging

jobs:
  performance-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 60

    services:
      mongodb:
        image: mongo:latest
        ports:
          - 27017:27017
        env:
          MONGO_INITDB_ROOT_USERNAME: root
          MONGO_INITDB_ROOT_PASSWORD: password
        options: >-
          --health-cmd "mongosh --eval 'db.adminCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    env:
      NODE_ENV: test
      MONGODB_TEST_URI: *******************************************************************************
      REDIS_TEST_URI: redis://localhost:6379/1
      PERFORMANCE_TEST_MODE: true
      MAX_RESPONSE_TIME: 500
      MIN_THROUGHPUT: 20
      MAX_ERROR_RATE: 2
      MAX_MEMORY_INCREASE: 100

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: npm run build

    - name: Wait for services
      run: |
        echo "Waiting for MongoDB..."
        timeout 60 bash -c 'until mongosh --host localhost:27017 --username root --password password --authenticationDatabase admin --eval "db.adminCommand(\"ping\")" > /dev/null 2>&1; do sleep 2; done'
        echo "Waiting for Redis..."
        timeout 60 bash -c 'until redis-cli -h localhost -p 6379 ping > /dev/null 2>&1; do sleep 2; done'
        echo "Services are ready"

    - name: Run performance tests
      run: |
        chmod +x ./scripts/performance-testing-ci.sh
        ./scripts/performance-testing-ci.sh ${{ github.event.inputs.environment || 'test' }} ${{ github.event.inputs.test_type || 'regression' }}

    - name: Upload performance results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: performance-results-${{ github.run_number }}
        path: performance-results/
        retention-days: 30

    - name: Upload performance report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: performance-report-${{ github.run_number }}
        path: performance-results/*/performance-report.html
        retention-days: 30

    - name: Comment PR with performance results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const path = require('path');
          
          // Find the latest results directory
          const resultsDir = fs.readdirSync('performance-results')
            .filter(dir => fs.statSync(path.join('performance-results', dir)).isDirectory())
            .sort()
            .pop();
          
          if (!resultsDir) {
            console.log('No performance results found');
            return;
          }
          
          const analysisFile = path.join('performance-results', resultsDir, 'performance-analysis.json');
          
          if (!fs.existsSync(analysisFile)) {
            console.log('No performance analysis found');
            return;
          }
          
          const analysis = JSON.parse(fs.readFileSync(analysisFile, 'utf8'));
          
          const statusEmoji = {
            'passed': '✅',
            'passed_with_warnings': '⚠️',
            'failed': '❌'
          };
          
          const comment = `
          ## 🚀 Performance Test Results
          
          **Status:** ${statusEmoji[analysis.status] || '❓'} ${analysis.status.toUpperCase()}
          
          ### 📊 Key Metrics
          - **Response Time:** ${analysis.performanceMetrics.responseTime || 'N/A'}ms
          - **Throughput:** ${analysis.performanceMetrics.throughput || 'N/A'} req/s
          - **Error Rate:** ${analysis.performanceMetrics.errorRate || 'N/A'}%
          - **Tests Passed:** ${analysis.totalTests - analysis.failedTests}/${analysis.totalTests}
          
          ### 💡 Recommendations
          ${analysis.recommendations.length > 0 
            ? analysis.recommendations.map(r => `- ${r}`).join('\n')
            : '- No recommendations - performance is within acceptable limits ✨'
          }
          
          <details>
          <summary>📈 Detailed Results</summary>
          
          \`\`\`json
          ${JSON.stringify(analysis, null, 2)}
          \`\`\`
          
          </details>
          
          ---
          *Performance test run: ${resultsDir}*
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

    - name: Set job status based on performance results
      if: always()
      run: |
        # Find the latest results directory
        RESULTS_DIR=$(ls -1t performance-results/ | head -n1)
        
        if [ -z "$RESULTS_DIR" ]; then
          echo "No performance results found"
          exit 1
        fi
        
        ANALYSIS_FILE="performance-results/$RESULTS_DIR/performance-analysis.json"
        
        if [ ! -f "$ANALYSIS_FILE" ]; then
          echo "No performance analysis found"
          exit 1
        fi
        
        STATUS=$(cat "$ANALYSIS_FILE" | grep '"status"' | cut -d'"' -f4)
        
        echo "Performance test status: $STATUS"
        
        case "$STATUS" in
          "passed")
            echo "✅ Performance tests passed"
            exit 0
            ;;
          "passed_with_warnings")
            echo "⚠️ Performance tests passed with warnings"
            exit 0
            ;;
          "failed")
            echo "❌ Performance tests failed"
            exit 1
            ;;
          *)
            echo "❓ Unknown performance test status: $STATUS"
            exit 1
            ;;
        esac

  performance-trend-analysis:
    runs-on: ubuntu-latest
    needs: performance-tests
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download performance results
      uses: actions/download-artifact@v4
      with:
        name: performance-results-${{ github.run_number }}
        path: performance-results/

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Generate trend analysis
      run: |
        # Create a simple trend analysis script
        cat > trend-analysis.js << 'EOF'
        const fs = require('fs');
        const path = require('path');
        
        // Find all performance analysis files
        const resultsDir = 'performance-results';
        const analysisFiles = [];
        
        function findAnalysisFiles(dir) {
          const items = fs.readdirSync(dir);
          for (const item of items) {
            const fullPath = path.join(dir, item);
            if (fs.statSync(fullPath).isDirectory()) {
              findAnalysisFiles(fullPath);
            } else if (item === 'performance-analysis.json') {
              analysisFiles.push(fullPath);
            }
          }
        }
        
        if (fs.existsSync(resultsDir)) {
          findAnalysisFiles(resultsDir);
        }
        
        console.log(`Found ${analysisFiles.length} performance analysis files`);
        
        // Generate trend report
        const trendReport = {
          timestamp: new Date().toISOString(),
          totalAnalyses: analysisFiles.length,
          summary: 'Performance trend analysis completed',
          files: analysisFiles
        };
        
        fs.writeFileSync('performance-trend-report.json', JSON.stringify(trendReport, null, 2));
        console.log('Trend analysis completed');
        EOF
        
        node trend-analysis.js

    - name: Upload trend analysis
      uses: actions/upload-artifact@v4
      with:
        name: performance-trend-analysis-${{ github.run_number }}
        path: performance-trend-report.json
        retention-days: 90
