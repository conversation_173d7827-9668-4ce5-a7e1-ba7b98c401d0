# Worksheet Question Management Performance Testing Suite

## Overview

This comprehensive performance testing suite provides automated performance testing, regression detection, capacity planning, and monitoring for the worksheet question management system. It includes load testing, benchmarking, stress testing, and performance trend analysis.

## Features

### 🚀 Performance Testing
- **Automated regression testing** with Jest integration
- **Load testing scenarios** for various user loads
- **Stress testing** for high-concurrency scenarios
- **Memory optimization testing** for large datasets
- **Cache performance testing** with hit rate analysis

### 📊 Capacity Planning
- **Current capacity metrics** analysis
- **Bottleneck identification** and recommendations
- **Scaling plan generation** (short/medium/long term)
- **Cost projections** and optimization savings
- **Resource utilization monitoring**

### 📈 Monitoring & Dashboards
- **Real-time performance dashboards** with Prometheus/Grafana
- **Automated alerting** for performance degradation
- **Performance trend analysis** over time
- **CI/CD integration** for continuous performance monitoring

## Quick Start

### Prerequisites

- Node.js 22+
- MongoDB (for testing)
- Redis (for caching tests)
- Docker (optional, for containerized testing)

### Installation

```bash
# Install dependencies
npm install

# Build the application
npm run build

# Set up test environment
export NODE_ENV=test
export MONGODB_TEST_URI=mongodb://localhost:27017/edusg_performance_test
export REDIS_TEST_URI=redis://localhost:6379/1
```

### Running Performance Tests

#### 1. Regression Testing (Jest)
```bash
# Run performance regression tests
npm test -- --testPathPattern="performance.regression.spec.ts"

# Run with coverage
npm run test:cov -- --testPathPattern="performance.regression.spec.ts"
```

#### 2. Load Testing (API)
```bash
# Start the application
npm run start:dev

# Run load tests via API
curl -X POST "http://localhost:3000/api/worksheet-questions/performance/load-test" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-admin-token"
```

#### 3. Automated CI/CD Testing
```bash
# Run the complete performance testing suite
./scripts/performance-testing-ci.sh test regression

# Run full performance tests (regression + load)
./scripts/performance-testing-ci.sh test full
```

## API Endpoints

### Performance Testing
- `POST /api/worksheet-questions/performance/test-suite` - Run complete performance test suite
- `POST /api/worksheet-questions/performance/custom-test` - Run custom performance test
- `POST /api/worksheet-questions/performance/load-test` - Run load testing scenarios
- `GET /api/worksheet-questions/performance/report` - Get performance report

### Capacity Planning
- `GET /api/worksheet-questions/capacity-planning/report` - Generate capacity planning report
- `GET /api/worksheet-questions/capacity-planning/metrics` - Get current capacity metrics
- `GET /api/worksheet-questions/capacity-planning/recommendations` - Get recommendations
- `GET /api/worksheet-questions/capacity-planning/cost-projections` - Get cost projections

### Metrics & Monitoring
- `GET /api/worksheet-questions/metrics` - Get Prometheus metrics
- `GET /api/worksheet-questions/metrics/summary` - Get performance summary

## Configuration

### Environment Variables

```bash
# Performance Testing
PERFORMANCE_TEST_MODE=true
MAX_RESPONSE_TIME=500          # milliseconds
MIN_THROUGHPUT=20              # requests per second
MAX_ERROR_RATE=2               # percentage
MAX_MEMORY_INCREASE=100        # MB

# Capacity Planning
EXPECTED_GROWTH_RATE=20        # percentage per month
MAX_REQUESTS_PER_SECOND=200
MAX_MEMORY_USAGE_PERCENT=80
BASE_MONTHLY_COST=1000         # USD

# Database
MONGODB_TEST_URI=mongodb://localhost:27017/edusg_performance_test
REDIS_TEST_URI=redis://localhost:6379/1
```

### Performance Thresholds

The system uses configurable thresholds for performance validation:

```typescript
const PERFORMANCE_BASELINES = {
  basicRetrieval: {
    maxAverageResponseTime: 150, // ms
    minThroughput: 40,          // requests/second
    maxErrorRate: 1,            // percentage
  },
  bulkOperations: {
    maxAverageResponseTime: 800, // ms
    minThroughput: 15,          // requests/second
    maxErrorRate: 2,            // percentage
  },
  // ... more configurations
};
```

## Dashboard Setup

### Prometheus Configuration

Add to your `prometheus.yml`:

```yaml
scrape_configs:
  - job_name: 'worksheet-questions'
    static_configs:
      - targets: ['localhost:3000']
    metrics_path: '/api/worksheet-questions/metrics'
    scrape_interval: 15s
```

### Grafana Dashboard

Import the dashboard configuration from:
```
config/monitoring/worksheet-question-performance-dashboard.json
```

Key panels include:
- API Response Time (95th/50th percentile)
- Request Rate by endpoint
- Error Rate percentage
- Database Query Performance
- Cache Hit Rate
- Memory Usage
- Active Operations
- Background Job Queue

## CI/CD Integration

### GitHub Actions

The performance testing suite integrates with GitHub Actions for:

- **Automated testing** on every PR and push
- **Daily performance monitoring** via scheduled runs
- **Performance regression detection** with PR comments
- **Trend analysis** for long-term monitoring

### Configuration

```yaml
# .github/workflows/performance-testing.yml
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM UTC
```

## Performance Test Types

### 1. Regression Tests
- **Purpose**: Detect performance regressions in new code
- **Frequency**: Every PR and commit
- **Duration**: 5-10 minutes
- **Scope**: Core functionality with baseline comparison

### 2. Load Tests
- **Purpose**: Validate system performance under expected load
- **Frequency**: Daily or on-demand
- **Duration**: 15-30 minutes
- **Scope**: Multiple concurrent users, realistic scenarios

### 3. Stress Tests
- **Purpose**: Find system breaking points
- **Frequency**: Weekly or before releases
- **Duration**: 30-60 minutes
- **Scope**: High concurrency, resource exhaustion scenarios

### 4. Capacity Planning
- **Purpose**: Predict future resource needs
- **Frequency**: Monthly or quarterly
- **Duration**: Variable (includes historical analysis)
- **Scope**: Growth projections, cost analysis, scaling recommendations

## Interpreting Results

### Performance Metrics

- **Response Time**: Time to complete a request
  - Excellent: < 100ms
  - Good: 100-500ms
  - Acceptable: 500ms-1s
  - Poor: > 1s

- **Throughput**: Requests processed per second
  - Minimum: 10 req/s
  - Good: 50 req/s
  - Excellent: 100+ req/s

- **Error Rate**: Percentage of failed requests
  - Excellent: < 1%
  - Acceptable: 1-2%
  - Poor: > 5%

- **Cache Hit Rate**: Percentage of cache hits
  - Minimum: 70%
  - Good: 85%
  - Excellent: 95%+

### Capacity Planning Recommendations

The system generates prioritized recommendations:

- **Critical**: Immediate action required (< 1 week)
- **High**: Important improvements (1-4 weeks)
- **Medium**: Beneficial optimizations (1-2 months)
- **Low**: Long-term considerations (3+ months)

## Troubleshooting

### Common Issues

1. **High Response Times**
   - Check database query performance
   - Verify index usage
   - Review cache hit rates
   - Monitor memory usage

2. **Low Throughput**
   - Check for bottlenecks in critical paths
   - Review connection pool settings
   - Consider horizontal scaling
   - Optimize resource allocation

3. **High Error Rates**
   - Review application logs
   - Check database connectivity
   - Verify rate limiting configuration
   - Monitor resource exhaustion

### Debug Mode

Enable detailed logging:

```bash
export DEBUG=worksheet:performance:*
export LOG_LEVEL=debug
npm run start:dev
```

## Best Practices

### Performance Testing
1. **Consistent Environment**: Use dedicated test environments
2. **Baseline Establishment**: Maintain performance baselines
3. **Regular Monitoring**: Run tests consistently
4. **Trend Analysis**: Track performance over time

### Capacity Planning
1. **Regular Reviews**: Monthly capacity assessments
2. **Growth Modeling**: Account for business growth
3. **Cost Optimization**: Balance performance and cost
4. **Proactive Scaling**: Plan ahead of demand

### Monitoring
1. **Real-time Alerts**: Set up performance alerts
2. **Dashboard Reviews**: Regular dashboard monitoring
3. **Incident Response**: Quick response to performance issues
4. **Post-incident Analysis**: Learn from performance incidents

## Contributing

### Adding New Tests

1. Create test configuration in `WorksheetQuestionPerformanceTestingService`
2. Add test scenarios to the performance test suite
3. Update baseline thresholds if needed
4. Document new test types

### Extending Capacity Planning

1. Add new metrics to `WorksheetQuestionCapacityPlanningService`
2. Implement recommendation logic
3. Update dashboard configurations
4. Add API endpoints for new features

## Support

For issues or questions:
- Check the troubleshooting section
- Review application logs
- Contact the development team
- Create an issue in the project repository
