import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { WorksheetQuestionMetricsService } from '../services/worksheet-question-metrics.service';
import { WorksheetQuestionBackgroundService, BulkOperationJob } from '../services/worksheet-question-background.service';
import { IExerciseQuestion } from '../../../shared/interfaces/exercise-question.interface';

/**
 * Bull queue processor for handling bulk worksheet question operations
 * Processes jobs asynchronously with progress tracking and error handling
 */
@Processor('worksheet-questions')
export class WorksheetQuestionBulkProcessor extends WorkerHost {
  private readonly logger = new Logger(WorksheetQuestionBulkProcessor.name);

  constructor(
    @InjectModel(WorksheetQuestionDocument.name)
    private worksheetQuestionModel: Model<WorksheetQuestionDocument>,
    private readonly metricsService: WorksheetQuestionMetricsService,
    private readonly backgroundService: WorksheetQuestionBackgroundService
  ) {
    super();
  }

  /**
   * Process bulk operations
   */
  async process(job: Job<BulkOperationJob>): Promise<any> {
    const { type, worksheetId, userId, data } = job.data;
    const startTime = Date.now();

    this.logger.log(`Processing ${type} job ${job.id} for worksheet ${worksheetId} by user ${userId}`);

    try {
      let result: any;

      switch (type) {
        case 'bulk_add':
          result = await this.processBulkAdd(job);
          break;
        case 'bulk_update':
          result = await this.processBulkUpdate(job);
          break;
        case 'bulk_delete':
          result = await this.processBulkDelete(job);
          break;
        case 'bulk_reorder':
          result = await this.processBulkReorder(job);
          break;
        case 'worksheet_export':
          result = await this.processWorksheetExport(job);
          break;
        case 'worksheet_import':
          result = await this.processWorksheetImport(job);
          break;
        default:
          throw new Error(`Unknown job type: ${type}`);
      }

      const duration = Date.now() - startTime;
      this.logger.log(`Completed ${type} job ${job.id} in ${duration}ms`);

      // Record metrics
      this.metricsService.recordDbQuery(duration / 1000, `bulk_${type}`, 'worksheet_questions', 'success');
      this.metricsService.updateActiveOperations(type, -1);

      // Notify user of completion
      await this.backgroundService.notifyJobCompletion(
        job.id!,
        userId,
        worksheetId,
        type,
        true,
        result
      );

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Failed ${type} job ${job.id} after ${duration}ms: ${error.message}`);

      // Record metrics
      this.metricsService.recordDbQuery(duration / 1000, `bulk_${type}`, 'worksheet_questions', 'error');
      this.metricsService.updateActiveOperations(type, -1);

      // Notify user of failure
      await this.backgroundService.notifyJobCompletion(
        job.id!,
        userId,
        worksheetId,
        type,
        false,
        null,
        error.message
      );

      throw error;
    }
  }

  /**
   * Process bulk add operation
   */
  private async processBulkAdd(job: Job<BulkOperationJob>): Promise<any> {
    const { worksheetId, userId, schoolId, data } = job.data;
    const { questions } = data;
    const batchSize = job.data.options?.batchSize || 10;

    let processed = 0;
    const results: any[] = [];

    // Get current worksheet document
    let worksheetDoc = await this.worksheetQuestionModel.findOne({ worksheetId });
    if (!worksheetDoc) {
      worksheetDoc = new this.worksheetQuestionModel({
        worksheetId,
        questions: [],
        totalQuestions: 0,
        schoolId,
        lastModifiedBy: userId,
        lastModifiedAt: new Date()
      });
    }

    // Process questions in batches
    for (let i = 0; i < questions.length; i += batchSize) {
      const batch = questions.slice(i, i + batchSize);
      
      // Add questions to the document
      for (const question of batch) {
        const enrichedQuestion: IExerciseQuestion = {
          ...question,
          id: question.id || this.generateQuestionId(),
          audit: {
            createdBy: userId,
            createdAt: new Date(),
            updatedBy: userId,
            updatedAt: new Date(),
            version: 1
          },
          schoolId
        };

        worksheetDoc.questions.push(enrichedQuestion);
        results.push(enrichedQuestion);
        processed++;

        // Update progress
        await job.updateProgress({
          completed: processed,
          total: questions.length,
          currentItem: `Adding question ${processed}/${questions.length}`
        });
      }

      // Save batch
      worksheetDoc.totalQuestions = worksheetDoc.questions.length;
      worksheetDoc.lastModifiedBy = userId;
      worksheetDoc.lastModifiedAt = new Date();
      await worksheetDoc.save();

      this.logger.debug(`Processed batch ${Math.floor(i / batchSize) + 1} for bulk add job ${job.id}`);
    }

    return {
      addedQuestions: results.length,
      totalQuestions: worksheetDoc.questions.length,
      worksheetId
    };
  }

  /**
   * Process bulk update operation
   */
  private async processBulkUpdate(job: Job<BulkOperationJob>): Promise<any> {
    const { worksheetId, userId, data } = job.data;
    const { updates } = data;
    const batchSize = job.data.options?.batchSize || 5;

    let processed = 0;
    const results: any[] = [];

    // Get worksheet document
    const worksheetDoc = await this.worksheetQuestionModel.findOne({ worksheetId });
    if (!worksheetDoc) {
      throw new Error(`Worksheet ${worksheetId} not found`);
    }

    // Process updates in batches
    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);
      
      for (const update of batch) {
        const questionIndex = worksheetDoc.questions.findIndex(q => q.id === update.questionId);
        if (questionIndex >= 0) {
          // Apply updates
          Object.assign(worksheetDoc.questions[questionIndex], update.updates);
          
          // Update audit info
          if (worksheetDoc.questions[questionIndex].audit) {
            worksheetDoc.questions[questionIndex].audit.updatedBy = userId;
            worksheetDoc.questions[questionIndex].audit.updatedAt = new Date();
            worksheetDoc.questions[questionIndex].audit.version = 
              (worksheetDoc.questions[questionIndex].audit.version || 1) + 1;
          }

          results.push({
            questionId: update.questionId,
            updated: true
          });
        } else {
          results.push({
            questionId: update.questionId,
            updated: false,
            error: 'Question not found'
          });
        }

        processed++;

        // Update progress
        await job.updateProgress({
          completed: processed,
          total: updates.length,
          currentItem: `Updating question ${processed}/${updates.length}`
        });
      }

      // Save batch
      worksheetDoc.lastModifiedBy = userId;
      worksheetDoc.lastModifiedAt = new Date();
      await worksheetDoc.save();

      this.logger.debug(`Processed batch ${Math.floor(i / batchSize) + 1} for bulk update job ${job.id}`);
    }

    return {
      updatedQuestions: results.filter(r => r.updated).length,
      failedUpdates: results.filter(r => !r.updated).length,
      details: results,
      worksheetId
    };
  }

  /**
   * Process bulk delete operation
   */
  private async processBulkDelete(job: Job<BulkOperationJob>): Promise<any> {
    const { worksheetId, userId, data } = job.data;
    const { questionIds } = data;
    const batchSize = job.data.options?.batchSize || 10;

    let processed = 0;
    const results: any[] = [];

    // Get worksheet document
    const worksheetDoc = await this.worksheetQuestionModel.findOne({ worksheetId });
    if (!worksheetDoc) {
      throw new Error(`Worksheet ${worksheetId} not found`);
    }

    // Process deletions in batches
    for (let i = 0; i < questionIds.length; i += batchSize) {
      const batch = questionIds.slice(i, i + batchSize);
      
      for (const questionId of batch) {
        const initialLength = worksheetDoc.questions.length;
        worksheetDoc.questions = worksheetDoc.questions.filter(q => q.id !== questionId);
        
        const deleted = worksheetDoc.questions.length < initialLength;
        results.push({
          questionId,
          deleted
        });

        processed++;

        // Update progress
        await job.updateProgress({
          completed: processed,
          total: questionIds.length,
          currentItem: `Deleting question ${processed}/${questionIds.length}`
        });
      }

      // Save batch
      worksheetDoc.totalQuestions = worksheetDoc.questions.length;
      worksheetDoc.lastModifiedBy = userId;
      worksheetDoc.lastModifiedAt = new Date();
      await worksheetDoc.save();

      this.logger.debug(`Processed batch ${Math.floor(i / batchSize) + 1} for bulk delete job ${job.id}`);
    }

    return {
      deletedQuestions: results.filter(r => r.deleted).length,
      failedDeletions: results.filter(r => !r.deleted).length,
      remainingQuestions: worksheetDoc.questions.length,
      worksheetId
    };
  }

  /**
   * Process bulk reorder operation
   */
  private async processBulkReorder(job: Job<BulkOperationJob>): Promise<any> {
    const { worksheetId, userId, data } = job.data;
    const { reorderMap } = data;

    // Get worksheet document
    const worksheetDoc = await this.worksheetQuestionModel.findOne({ worksheetId });
    if (!worksheetDoc) {
      throw new Error(`Worksheet ${worksheetId} not found`);
    }

    // Create a map for quick lookup
    const positionMap = new Map(reorderMap.map(item => [item.questionId, item.newPosition]));

    // Sort questions by new position
    worksheetDoc.questions.sort((a, b) => {
      const posA = positionMap.get(a.id!) || 999999;
      const posB = positionMap.get(b.id!) || 999999;
      return posA - posB;
    });

    // Update positions
    worksheetDoc.questions.forEach((question, index) => {
      question.position = index + 1;
      if (question.audit) {
        question.audit.updatedBy = userId;
        question.audit.updatedAt = new Date();
        question.audit.version = (question.audit.version || 1) + 1;
      }
    });

    // Update progress
    await job.updateProgress({
      completed: reorderMap.length,
      total: reorderMap.length,
      currentItem: 'Finalizing reorder operation'
    });

    // Save changes
    worksheetDoc.lastModifiedBy = userId;
    worksheetDoc.lastModifiedAt = new Date();
    await worksheetDoc.save();

    return {
      reorderedQuestions: reorderMap.length,
      totalQuestions: worksheetDoc.questions.length,
      worksheetId
    };
  }

  /**
   * Process worksheet export operation
   */
  private async processWorksheetExport(job: Job<BulkOperationJob>): Promise<any> {
    const { worksheetId, data } = job.data;
    const { exportFormat, options } = data;

    // Update progress
    await job.updateProgress({
      completed: 0,
      total: 100,
      currentItem: 'Starting export process'
    });

    // Get worksheet document
    const worksheetDoc = await this.worksheetQuestionModel.findOne({ worksheetId });
    if (!worksheetDoc) {
      throw new Error(`Worksheet ${worksheetId} not found`);
    }

    await job.updateProgress({
      completed: 25,
      total: 100,
      currentItem: 'Processing questions'
    });

    // Simulate export processing (in real implementation, this would generate the actual file)
    const exportData = {
      worksheetId,
      format: exportFormat,
      questionCount: worksheetDoc.questions.length,
      exportedAt: new Date().toISOString(),
      // In real implementation, this would be the file URL or download link
      downloadUrl: `/api/worksheets/${worksheetId}/export/${exportFormat}`,
      options
    };

    await job.updateProgress({
      completed: 100,
      total: 100,
      currentItem: 'Export completed'
    });

    return exportData;
  }

  /**
   * Process worksheet import operation
   */
  private async processWorksheetImport(job: Job<BulkOperationJob>): Promise<any> {
    // This would handle importing questions from various formats
    // Implementation would depend on the specific import requirements
    return {
      message: 'Import functionality would be implemented here',
      worksheetId: job.data.worksheetId
    };
  }

  /**
   * Generate a unique question ID
   */
  private generateQuestionId(): string {
    return `q_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Handle job completion
   */
  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.log(`Job ${job.id} completed successfully`);
  }

  /**
   * Handle job failure
   */
  @OnWorkerEvent('failed')
  onFailed(job: Job, err: Error) {
    this.logger.error(`Job ${job.id} failed: ${err.message}`);
  }

  /**
   * Handle job progress
   */
  @OnWorkerEvent('progress')
  onProgress(job: Job, progress: any) {
    this.logger.debug(`Job ${job.id} progress: ${progress.completed}/${progress.total} - ${progress.currentItem}`);
  }
}
