import { Controller, Get, Post, UseGuards, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '../../auth/guards/auth.guard';
import { RoleGuard } from '../../auth/guards/role.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { ActiveUser } from '../../auth/decorators/active-user.decorator';
import { EUserRole } from '../../user/dto/create-user.dto';
import { User } from '../../user/entities/user.entity';
import { WorksheetQuestionCapacityPlanningService, CapacityPlanningReport } from '../services/worksheet-question-capacity-planning.service';

/**
 * Controller for worksheet question capacity planning and performance analysis
 */
@ApiTags('Worksheet Question Capacity Planning')
@ApiBearerAuth()
@Controller('worksheet-questions/capacity-planning')
@UseGuards(AuthGuard, RoleGuard)
export class WorksheetQuestionCapacityPlanningController {
  constructor(
    private readonly capacityPlanningService: WorksheetQuestionCapacityPlanningService
  ) {}

  /**
   * Generate comprehensive capacity planning report
   */
  @Get('report')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Generate capacity planning report',
    description: 'Generates a comprehensive capacity planning report including current metrics, bottlenecks, recommendations, and cost projections'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Capacity planning report generated successfully',
    schema: {
      type: 'object',
      properties: {
        timestamp: { type: 'string', format: 'date-time' },
        currentCapacity: {
          type: 'object',
          properties: {
            currentLoad: {
              type: 'object',
              properties: {
                requestsPerSecond: { type: 'number' },
                averageResponseTime: { type: 'number' },
                errorRate: { type: 'number' },
                memoryUsage: { type: 'number' },
                cpuUsage: { type: 'number' }
              }
            },
            projectedLoad: {
              type: 'object',
              properties: {
                expectedGrowthRate: { type: 'number' },
                timeHorizon: { type: 'number' },
                projectedRequestsPerSecond: { type: 'number' },
                projectedMemoryUsage: { type: 'number' }
              }
            },
            systemLimits: {
              type: 'object',
              properties: {
                maxRequestsPerSecond: { type: 'number' },
                maxMemoryUsage: { type: 'number' },
                maxResponseTime: { type: 'number' },
                maxErrorRate: { type: 'number' }
              }
            }
          }
        },
        bottlenecks: {
          type: 'array',
          items: { type: 'string' }
        },
        recommendations: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              priority: { type: 'string', enum: ['low', 'medium', 'high', 'critical'] },
              category: { type: 'string', enum: ['scaling', 'optimization', 'infrastructure', 'monitoring'] },
              title: { type: 'string' },
              description: { type: 'string' },
              impact: { type: 'string' },
              effort: { type: 'string', enum: ['low', 'medium', 'high'] },
              timeline: { type: 'string' },
              implementation: {
                type: 'array',
                items: { type: 'string' }
              }
            }
          }
        },
        scalingPlan: {
          type: 'object',
          properties: {
            shortTerm: { type: 'array', items: { $ref: '#/components/schemas/CapacityRecommendation' } },
            mediumTerm: { type: 'array', items: { $ref: '#/components/schemas/CapacityRecommendation' } },
            longTerm: { type: 'array', items: { $ref: '#/components/schemas/CapacityRecommendation' } }
          }
        },
        costProjections: {
          type: 'object',
          properties: {
            currentMonthlyCost: { type: 'number' },
            projectedMonthlyCost: { type: 'number' },
            optimizationSavings: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin or School Manager access required' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async generateCapacityPlanningReport(@ActiveUser() user: User): Promise<CapacityPlanningReport> {
    const startTime = Date.now();
    
    const report = await this.capacityPlanningService.generateCapacityPlanningReport();
    
    const totalDuration = Date.now() - startTime;

    // Add execution metadata
    const enrichedReport = {
      ...report,
      executionInfo: {
        totalDuration: `${totalDuration}ms`,
        generatedBy: user.id,
        generatedAt: new Date().toISOString(),
        userRole: user.role
      }
    };

    return enrichedReport;
  }

  /**
   * Get current capacity metrics summary
   */
  @Get('metrics')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Get current capacity metrics',
    description: 'Returns current system capacity metrics including load, performance, and resource utilization'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Current capacity metrics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        currentLoad: {
          type: 'object',
          properties: {
            requestsPerSecond: { type: 'number', description: 'Current requests per second' },
            averageResponseTime: { type: 'number', description: 'Average response time in milliseconds' },
            errorRate: { type: 'number', description: 'Error rate percentage' },
            memoryUsage: { type: 'number', description: 'Memory usage percentage' },
            cpuUsage: { type: 'number', description: 'CPU usage percentage' }
          }
        },
        systemHealth: {
          type: 'object',
          properties: {
            status: { type: 'string', enum: ['healthy', 'warning', 'critical'] },
            issues: { type: 'array', items: { type: 'string' } },
            recommendations: { type: 'array', items: { type: 'string' } }
          }
        },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin or School Manager access required' })
  async getCurrentCapacityMetrics(@ActiveUser() user: User) {
    const report = await this.capacityPlanningService.generateCapacityPlanningReport();
    
    // Determine system health status
    let healthStatus = 'healthy';
    const issues: string[] = [];
    const quickRecommendations: string[] = [];

    // Check for critical issues
    if (report.currentCapacity.currentLoad.errorRate > 5) {
      healthStatus = 'critical';
      issues.push('High error rate detected');
      quickRecommendations.push('Investigate error causes immediately');
    }

    if (report.currentCapacity.currentLoad.averageResponseTime > 1000) {
      healthStatus = healthStatus === 'critical' ? 'critical' : 'warning';
      issues.push('High response time detected');
      quickRecommendations.push('Consider database optimization');
    }

    if (report.currentCapacity.currentLoad.memoryUsage > 80) {
      healthStatus = healthStatus === 'critical' ? 'critical' : 'warning';
      issues.push('High memory usage detected');
      quickRecommendations.push('Monitor memory usage and consider optimization');
    }

    return {
      currentLoad: report.currentCapacity.currentLoad,
      systemHealth: {
        status: healthStatus,
        issues,
        recommendations: quickRecommendations
      },
      timestamp: new Date().toISOString(),
      executionInfo: {
        retrievedBy: user.id,
        retrievedAt: new Date().toISOString(),
        userRole: user.role
      }
    };
  }

  /**
   * Get capacity planning recommendations
   */
  @Get('recommendations')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Get capacity planning recommendations',
    description: 'Returns prioritized recommendations for capacity planning and performance optimization'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Capacity planning recommendations retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        recommendations: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              priority: { type: 'string', enum: ['low', 'medium', 'high', 'critical'] },
              category: { type: 'string', enum: ['scaling', 'optimization', 'infrastructure', 'monitoring'] },
              title: { type: 'string' },
              description: { type: 'string' },
              impact: { type: 'string' },
              effort: { type: 'string', enum: ['low', 'medium', 'high'] },
              timeline: { type: 'string' },
              implementation: { type: 'array', items: { type: 'string' } }
            }
          }
        },
        prioritizedActions: {
          type: 'object',
          properties: {
            immediate: { type: 'array', items: { type: 'string' } },
            shortTerm: { type: 'array', items: { type: 'string' } },
            longTerm: { type: 'array', items: { type: 'string' } }
          }
        },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin or School Manager access required' })
  async getCapacityPlanningRecommendations(@ActiveUser() user: User) {
    const report = await this.capacityPlanningService.generateCapacityPlanningReport();
    
    // Categorize recommendations by urgency
    const immediate = report.recommendations
      .filter(r => r.priority === 'critical')
      .map(r => r.title);
    
    const shortTerm = report.recommendations
      .filter(r => r.priority === 'high' || (r.priority === 'medium' && r.timeline.includes('1-2 weeks')))
      .map(r => r.title);
    
    const longTerm = report.recommendations
      .filter(r => r.priority === 'low' || r.timeline.includes('months'))
      .map(r => r.title);

    return {
      recommendations: report.recommendations,
      prioritizedActions: {
        immediate,
        shortTerm,
        longTerm
      },
      timestamp: new Date().toISOString(),
      executionInfo: {
        retrievedBy: user.id,
        retrievedAt: new Date().toISOString(),
        userRole: user.role
      }
    };
  }

  /**
   * Get cost projections
   */
  @Get('cost-projections')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Get cost projections',
    description: 'Returns cost projections based on current usage and growth trends'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Cost projections retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        currentMonthlyCost: { type: 'number', description: 'Current monthly cost in USD' },
        projectedMonthlyCost: { type: 'number', description: 'Projected monthly cost in USD' },
        optimizationSavings: { type: 'number', description: 'Potential savings from optimizations in USD' },
        breakdown: {
          type: 'object',
          properties: {
            infrastructure: { type: 'number' },
            storage: { type: 'number' },
            bandwidth: { type: 'number' },
            monitoring: { type: 'number' }
          }
        },
        recommendations: {
          type: 'array',
          items: { type: 'string' }
        },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin or School Manager access required' })
  async getCostProjections(@ActiveUser() user: User) {
    const report = await this.capacityPlanningService.generateCapacityPlanningReport();
    
    // Enhanced cost breakdown (simplified for demo)
    const costBreakdown = {
      infrastructure: report.costProjections.currentMonthlyCost * 0.6,
      storage: report.costProjections.currentMonthlyCost * 0.2,
      bandwidth: report.costProjections.currentMonthlyCost * 0.15,
      monitoring: report.costProjections.currentMonthlyCost * 0.05
    };

    const costRecommendations = [
      'Consider reserved instances for predictable workloads',
      'Implement auto-scaling to optimize resource usage',
      'Review storage retention policies',
      'Optimize data transfer costs'
    ];

    return {
      ...report.costProjections,
      breakdown: costBreakdown,
      recommendations: costRecommendations,
      timestamp: new Date().toISOString(),
      executionInfo: {
        retrievedBy: user.id,
        retrievedAt: new Date().toISOString(),
        userRole: user.role
      }
    };
  }
}
