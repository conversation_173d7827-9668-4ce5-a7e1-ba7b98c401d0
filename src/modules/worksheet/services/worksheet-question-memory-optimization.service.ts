import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Transform, Readable } from 'stream';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { WorksheetQuestionMetricsService } from './worksheet-question-metrics.service';
import { IExerciseQuestion } from '../../../shared/interfaces/exercise-question.interface';

export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: any;
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  memoryUsage: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
  };
}

export interface StreamingOptions {
  batchSize?: number;
  highWaterMark?: number;
  encoding?: string;
  objectMode?: boolean;
}

/**
 * Memory optimization service for worksheet question operations
 * Implements pagination, streaming, lazy loading, and memory-efficient data structures
 */
@Injectable()
export class WorksheetQuestionMemoryOptimizationService {
  private readonly logger = new Logger(WorksheetQuestionMemoryOptimizationService.name);

  // Memory optimization configuration
  private readonly MEMORY_CONFIG = {
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
    STREAMING_BATCH_SIZE: 50,
    MEMORY_THRESHOLD_MB: 100,
    LAZY_LOAD_THRESHOLD: 1000,
    CACHE_CLEANUP_INTERVAL: 300000, // 5 minutes
  };

  // Memory monitoring
  private memoryAlerts: Map<string, number> = new Map();
  private lastMemoryCheck = Date.now();

  constructor(
    @InjectModel(WorksheetQuestionDocument.name)
    private worksheetQuestionModel: Model<WorksheetQuestionDocument>,
    private readonly metricsService: WorksheetQuestionMetricsService,
    private readonly configService: ConfigService
  ) {
    // Start memory monitoring
    this.startMemoryMonitoring();
  }

  /**
   * Get paginated worksheet questions with memory optimization
   */
  async getPaginatedQuestions(
    worksheetId: string,
    options: PaginationOptions
  ): Promise<PaginatedResult<IExerciseQuestion>> {
    const startMemory = process.memoryUsage();
    const startTime = Date.now();

    try {
      // Validate and sanitize pagination options
      const sanitizedOptions = this.sanitizePaginationOptions(options);
      const { page, limit, sortBy, sortOrder, filters } = sanitizedOptions;

      // Calculate skip value
      const skip = (page - 1) * limit;

      // Build query
      const query = { worksheetId, ...filters };

      // Build sort object
      const sort: any = {};
      if (sortBy) {
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
      } else {
        sort.position = 1; // Default sort by position
      }

      // Execute queries in parallel for better performance
      const [questions, total] = await Promise.all([
        this.worksheetQuestionModel
          .findOne(query)
          .select('questions')
          .lean()
          .exec()
          .then(doc => {
            if (!doc || !doc.questions) return [];
            
            // Apply sorting and pagination to the questions array
            let sortedQuestions = [...doc.questions];
            
            // Apply sorting
            if (sortBy && sortBy !== 'position') {
              sortedQuestions.sort((a, b) => {
                const aVal = this.getNestedValue(a, sortBy);
                const bVal = this.getNestedValue(b, sortBy);
                const comparison = aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
                return sortOrder === 'desc' ? -comparison : comparison;
              });
            }
            
            // Apply pagination
            return sortedQuestions.slice(skip, skip + limit);
          }),
        this.worksheetQuestionModel
          .findOne(query)
          .select('totalQuestions')
          .lean()
          .exec()
          .then(doc => doc?.totalQuestions || 0)
      ]);

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      // Monitor memory usage
      const endMemory = process.memoryUsage();
      const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;
      
      this.metricsService.updateMemoryUsage('paginated_query', endMemory.heapUsed);
      
      if (memoryDelta > this.MEMORY_CONFIG.MEMORY_THRESHOLD_MB * 1024 * 1024) {
        this.logger.warn(`High memory usage detected in pagination: ${Math.round(memoryDelta / 1024 / 1024)}MB`);
      }

      const duration = Date.now() - startTime;
      this.logger.debug(`Paginated query completed in ${duration}ms, returned ${questions.length} questions`);

      return {
        data: questions,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext,
          hasPrev
        },
        memoryUsage: {
          heapUsed: endMemory.heapUsed,
          heapTotal: endMemory.heapTotal,
          external: endMemory.external,
          rss: endMemory.rss
        }
      };
    } catch (error) {
      this.logger.error(`Error in paginated query: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a streaming interface for large worksheet exports
   */
  createQuestionStream(
    worksheetId: string,
    options: StreamingOptions = {}
  ): Readable {
    const {
      batchSize = this.MEMORY_CONFIG.STREAMING_BATCH_SIZE,
      highWaterMark = 16 * 1024, // 16KB
      objectMode = true
    } = options;

    let currentBatch = 0;
    let totalQuestions = 0;
    let processedQuestions = 0;

    const stream = new Readable({
      objectMode,
      highWaterMark,
      async read() {
        try {
          if (currentBatch === 0) {
            // Get total count first
            const doc = await this.worksheetQuestionModel
              .findOne({ worksheetId })
              .select('totalQuestions')
              .lean();
            totalQuestions = doc?.totalQuestions || 0;
          }

          if (processedQuestions >= totalQuestions) {
            // End of stream
            this.push(null);
            return;
          }

          // Get next batch
          const doc = await this.worksheetQuestionModel
            .findOne({ worksheetId })
            .select('questions')
            .lean();

          if (!doc || !doc.questions) {
            this.push(null);
            return;
          }

          const startIndex = currentBatch * batchSize;
          const endIndex = Math.min(startIndex + batchSize, doc.questions.length);
          const batch = doc.questions.slice(startIndex, endIndex);

          if (batch.length === 0) {
            this.push(null);
            return;
          }

          // Push batch to stream
          for (const question of batch) {
            this.push(this.optimizeQuestionForStreaming(question));
            processedQuestions++;
          }

          currentBatch++;

          // Monitor memory usage
          const memoryUsage = process.memoryUsage();
          this.metricsService.updateMemoryUsage('streaming_export', memoryUsage.heapUsed);

        } catch (error) {
          this.destroy(error);
        }
      }
    });

    return stream;
  }

  /**
   * Implement lazy loading for question details
   */
  async lazyLoadQuestionDetails(
    worksheetId: string,
    questionId: string,
    fields: string[] = []
  ): Promise<Partial<IExerciseQuestion> | null> {
    const startTime = Date.now();

    try {
      // Build projection object for specific fields
      const projection: any = {};
      if (fields.length > 0) {
        fields.forEach(field => {
          projection[`questions.$.${field}`] = 1;
        });
        projection['questions.$.id'] = 1; // Always include ID
      } else {
        projection['questions.$'] = 1; // Get full question if no specific fields
      }

      const doc = await this.worksheetQuestionModel
        .findOne(
          { worksheetId, 'questions.id': questionId },
          projection
        )
        .lean();

      if (!doc || !doc.questions || doc.questions.length === 0) {
        return null;
      }

      const question = doc.questions[0];
      
      // Monitor memory usage
      const memoryUsage = process.memoryUsage();
      this.metricsService.updateMemoryUsage('lazy_load', memoryUsage.heapUsed);

      const duration = Date.now() - startTime;
      this.logger.debug(`Lazy loaded question ${questionId} in ${duration}ms`);

      return question;
    } catch (error) {
      this.logger.error(`Error in lazy loading: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create memory-efficient data structures for question processing
   */
  createMemoryEfficientQuestionProcessor(): Transform {
    return new Transform({
      objectMode: true,
      transform(question: IExerciseQuestion, encoding, callback) {
        try {
          // Optimize question object for memory efficiency
          const optimized = this.optimizeQuestionStructure(question);
          callback(null, optimized);
        } catch (error) {
          callback(error);
        }
      }
    });
  }

  /**
   * Get memory usage statistics
   */
  getMemoryStatistics(): any {
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = {
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024),
      rss: Math.round(memoryUsage.rss / 1024 / 1024)
    };

    return {
      current: memoryUsageMB,
      thresholds: {
        warning: this.MEMORY_CONFIG.MEMORY_THRESHOLD_MB,
        critical: this.MEMORY_CONFIG.MEMORY_THRESHOLD_MB * 2
      },
      alerts: Array.from(this.memoryAlerts.entries()).map(([operation, count]) => ({
        operation,
        alertCount: count
      })),
      configuration: this.MEMORY_CONFIG,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Trigger garbage collection and memory cleanup
   */
  async performMemoryCleanup(): Promise<void> {
    try {
      this.logger.log('Starting memory cleanup...');

      // Clear memory alerts older than 1 hour
      const oneHourAgo = Date.now() - 3600000;
      if (this.lastMemoryCheck < oneHourAgo) {
        this.memoryAlerts.clear();
        this.lastMemoryCheck = Date.now();
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
        this.logger.debug('Garbage collection triggered');
      }

      // Update memory metrics
      const memoryUsage = process.memoryUsage();
      this.metricsService.updateMemoryUsage('cleanup', memoryUsage.heapUsed);

      this.logger.log('Memory cleanup completed');
    } catch (error) {
      this.logger.error(`Error during memory cleanup: ${error.message}`);
    }
  }

  /**
   * Sanitize pagination options
   */
  private sanitizePaginationOptions(options: PaginationOptions): PaginationOptions {
    return {
      page: Math.max(1, options.page || 1),
      limit: Math.min(this.MEMORY_CONFIG.MAX_PAGE_SIZE, Math.max(1, options.limit || this.MEMORY_CONFIG.DEFAULT_PAGE_SIZE)),
      sortBy: options.sortBy || 'position',
      sortOrder: options.sortOrder === 'desc' ? 'desc' : 'asc',
      filters: options.filters || {}
    };
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Optimize question object for streaming
   */
  private optimizeQuestionForStreaming(question: IExerciseQuestion): any {
    // Remove heavy fields for streaming to reduce memory usage
    const optimized = { ...question };
    
    // Remove large media content if present
    if (optimized.media && optimized.media.length > 1000) {
      optimized.media = `[Large media content - ${optimized.media.length} chars]`;
    }

    // Truncate long content for streaming
    if (optimized.content && optimized.content.length > 500) {
      optimized.content = optimized.content.substring(0, 500) + '...';
    }

    return optimized;
  }

  /**
   * Optimize question structure for memory efficiency
   */
  optimizeQuestionStructure(question: IExerciseQuestion): any {
    // Create a memory-efficient version of the question
    const optimized: any = {
      id: question.id,
      type: question.type,
      content: question.content,
      // Only include essential fields to reduce memory footprint
    };

    // Add optional fields only if they exist and are needed
    if (question.options && question.options.length > 0) {
      optimized.options = question.options;
    }

    if (question.answer) {
      optimized.answer = question.answer;
    }

    if (question.difficulty) {
      optimized.difficulty = question.difficulty;
    }

    return optimized;
  }

  /**
   * Start memory monitoring
   */
  private startMemoryMonitoring(): void {
    setInterval(() => {
      const memoryUsage = process.memoryUsage();
      const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;

      if (heapUsedMB > this.MEMORY_CONFIG.MEMORY_THRESHOLD_MB) {
        const alertKey = 'high_memory_usage';
        const currentCount = this.memoryAlerts.get(alertKey) || 0;
        this.memoryAlerts.set(alertKey, currentCount + 1);

        this.logger.warn(`High memory usage detected: ${Math.round(heapUsedMB)}MB`);
        
        // Trigger cleanup if memory usage is very high
        if (heapUsedMB > this.MEMORY_CONFIG.MEMORY_THRESHOLD_MB * 2) {
          this.performMemoryCleanup();
        }
      }

      // Update metrics
      this.metricsService.updateMemoryUsage('monitoring', memoryUsage.heapUsed);
    }, this.MEMORY_CONFIG.CACHE_CLEANUP_INTERVAL);
  }
}
