#!/bin/bash

# Worksheet Question Performance Testing CI/CD Script
# This script runs automated performance tests and validates performance regression
# Usage: ./scripts/performance-testing-ci.sh [environment] [test-type]

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
ENVIRONMENT="${1:-test}"
TEST_TYPE="${2:-regression}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RESULTS_DIR="$PROJECT_ROOT/performance-results/$TIMESTAMP"
LOG_FILE="$RESULTS_DIR/performance-test.log"

# Performance thresholds (exit codes will be set based on these)
MAX_RESPONSE_TIME=500  # milliseconds
MIN_THROUGHPUT=20      # requests per second
MAX_ERROR_RATE=2       # percentage
MAX_MEMORY_INCREASE=100 # MB

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Setup function
setup_environment() {
    log_info "Setting up performance testing environment..."
    
    # Create results directory
    mkdir -p "$RESULTS_DIR"
    
    # Set environment variables
    export NODE_ENV="$ENVIRONMENT"
    export PERFORMANCE_TEST_MODE="true"
    export MONGODB_TEST_URI="${MONGODB_TEST_URI:-mongodb://localhost:27017/edusg_performance_test}"
    export REDIS_TEST_URI="${REDIS_TEST_URI:-redis://localhost:6379/1}"
    
    log_info "Environment: $ENVIRONMENT"
    log_info "Results directory: $RESULTS_DIR"
    log_info "Log file: $LOG_FILE"
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    cd "$PROJECT_ROOT"
    
    if [ -f "package-lock.json" ]; then
        npm ci --silent
    else
        npm install --silent
    fi
    
    log_success "Dependencies installed successfully"
}

# Build application
build_application() {
    log_info "Building application..."
    cd "$PROJECT_ROOT"
    
    npm run build > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log_success "Application built successfully"
    else
        log_error "Application build failed"
        exit 1
    fi
}

# Start test services
start_test_services() {
    log_info "Starting test services..."
    
    # Start MongoDB if not running
    if ! pgrep -x "mongod" > /dev/null; then
        log_info "Starting MongoDB..."
        mongod --dbpath /tmp/mongodb-test --port 27017 --fork --logpath /tmp/mongodb-test.log
    fi
    
    # Start Redis if not running
    if ! pgrep -x "redis-server" > /dev/null; then
        log_info "Starting Redis..."
        redis-server --port 6379 --daemonize yes
    fi
    
    # Wait for services to be ready
    sleep 5
    
    log_success "Test services started"
}

# Run performance regression tests
run_regression_tests() {
    log_info "Running performance regression tests..."
    cd "$PROJECT_ROOT"
    
    # Run Jest performance tests
    npm test -- --testPathPattern="performance.regression.spec.ts" --verbose --json --outputFile="$RESULTS_DIR/jest-results.json" > "$RESULTS_DIR/jest-output.log" 2>&1
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_success "Performance regression tests passed"
    else
        log_error "Performance regression tests failed with exit code $exit_code"
        return $exit_code
    fi
    
    return 0
}

# Run load testing scenarios
run_load_tests() {
    log_info "Running load testing scenarios..."
    
    # Start the application in test mode
    npm run start:dev > "$RESULTS_DIR/app.log" 2>&1 &
    local app_pid=$!
    
    # Wait for application to start
    sleep 10
    
    # Run load tests via API
    curl -X POST "http://localhost:3000/api/worksheet-questions/performance/load-test" \
         -H "Content-Type: application/json" \
         -H "Authorization: Bearer test-admin-token" \
         -o "$RESULTS_DIR/load-test-results.json" \
         -w "%{http_code}" > "$RESULTS_DIR/load-test-status.txt" 2>&1
    
    local http_status=$(cat "$RESULTS_DIR/load-test-status.txt")
    
    # Stop the application
    kill $app_pid 2>/dev/null || true
    
    if [ "$http_status" = "200" ]; then
        log_success "Load tests completed successfully"
    else
        log_error "Load tests failed with HTTP status $http_status"
        return 1
    fi
    
    return 0
}

# Analyze performance results
analyze_results() {
    log_info "Analyzing performance results..."
    
    local results_file="$RESULTS_DIR/jest-results.json"
    local load_results_file="$RESULTS_DIR/load-test-results.json"
    local analysis_file="$RESULTS_DIR/performance-analysis.json"
    
    # Create analysis script
    cat > "$RESULTS_DIR/analyze.js" << 'EOF'
const fs = require('fs');
const path = require('path');

const resultsDir = process.argv[2];
const jestResults = JSON.parse(fs.readFileSync(path.join(resultsDir, 'jest-results.json'), 'utf8'));
const loadResults = fs.existsSync(path.join(resultsDir, 'load-test-results.json')) 
    ? JSON.parse(fs.readFileSync(path.join(resultsDir, 'load-test-results.json'), 'utf8'))
    : null;

const analysis = {
    timestamp: new Date().toISOString(),
    testsPassed: jestResults.success,
    totalTests: jestResults.numTotalTests,
    failedTests: jestResults.numFailedTests,
    testDuration: jestResults.testResults.reduce((sum, test) => sum + test.perfStats.runtime, 0),
    performanceMetrics: {
        responseTime: null,
        throughput: null,
        errorRate: null,
        memoryUsage: null
    },
    recommendations: [],
    status: 'unknown'
};

// Extract performance metrics from test results
if (jestResults.testResults && jestResults.testResults.length > 0) {
    const perfTest = jestResults.testResults.find(test => 
        test.testFilePath.includes('performance.regression.spec.ts')
    );
    
    if (perfTest && perfTest.assertionResults) {
        // Parse console output for performance metrics
        const consoleOutput = perfTest.console || [];
        consoleOutput.forEach(log => {
            if (log.message.includes('Average Response Time:')) {
                const match = log.message.match(/(\d+)ms/);
                if (match) analysis.performanceMetrics.responseTime = parseInt(match[1]);
            }
            if (log.message.includes('Throughput:')) {
                const match = log.message.match(/(\d+\.?\d*) req\/s/);
                if (match) analysis.performanceMetrics.throughput = parseFloat(match[1]);
            }
            if (log.message.includes('Error Rate:')) {
                const match = log.message.match(/(\d+\.?\d*)%/);
                if (match) analysis.performanceMetrics.errorRate = parseFloat(match[1]);
            }
        });
    }
}

// Add load test metrics if available
if (loadResults && loadResults.summary) {
    analysis.loadTestMetrics = {
        averageResponseTime: loadResults.summary.averageResponseTime,
        averageThroughput: loadResults.summary.averageThroughput,
        averageErrorRate: loadResults.summary.averageErrorRate
    };
}

// Generate recommendations based on metrics
const thresholds = {
    maxResponseTime: parseInt(process.env.MAX_RESPONSE_TIME || '500'),
    minThroughput: parseInt(process.env.MIN_THROUGHPUT || '20'),
    maxErrorRate: parseInt(process.env.MAX_ERROR_RATE || '2')
};

if (analysis.performanceMetrics.responseTime > thresholds.maxResponseTime) {
    analysis.recommendations.push('Response time exceeds threshold - consider database optimization');
}

if (analysis.performanceMetrics.throughput < thresholds.minThroughput) {
    analysis.recommendations.push('Throughput below threshold - consider horizontal scaling');
}

if (analysis.performanceMetrics.errorRate > thresholds.maxErrorRate) {
    analysis.recommendations.push('Error rate above threshold - investigate error causes');
}

// Determine overall status
if (jestResults.success && analysis.recommendations.length === 0) {
    analysis.status = 'passed';
} else if (jestResults.success && analysis.recommendations.length > 0) {
    analysis.status = 'passed_with_warnings';
} else {
    analysis.status = 'failed';
}

fs.writeFileSync(path.join(resultsDir, 'performance-analysis.json'), JSON.stringify(analysis, null, 2));
console.log(JSON.stringify(analysis, null, 2));
EOF
    
    # Run analysis
    node "$RESULTS_DIR/analyze.js" "$RESULTS_DIR" > "$RESULTS_DIR/analysis-output.log" 2>&1
    
    if [ -f "$analysis_file" ]; then
        log_success "Performance analysis completed"
        
        # Display key metrics
        local status=$(cat "$analysis_file" | grep '"status"' | cut -d'"' -f4)
        local response_time=$(cat "$analysis_file" | grep '"responseTime"' | cut -d':' -f2 | tr -d ' ,')
        local throughput=$(cat "$analysis_file" | grep '"throughput"' | cut -d':' -f2 | tr -d ' ,')
        local error_rate=$(cat "$analysis_file" | grep '"errorRate"' | cut -d':' -f2 | tr -d ' ,')
        
        log_info "Performance Analysis Results:"
        log_info "  Status: $status"
        log_info "  Response Time: ${response_time}ms"
        log_info "  Throughput: ${throughput} req/s"
        log_info "  Error Rate: ${error_rate}%"
        
        # Set exit code based on status
        case "$status" in
            "passed")
                return 0
                ;;
            "passed_with_warnings")
                log_warning "Performance tests passed but with warnings"
                return 0
                ;;
            "failed")
                log_error "Performance tests failed"
                return 1
                ;;
            *)
                log_error "Unknown performance test status: $status"
                return 1
                ;;
        esac
    else
        log_error "Performance analysis failed"
        return 1
    fi
}

# Generate performance report
generate_report() {
    log_info "Generating performance report..."
    
    local report_file="$RESULTS_DIR/performance-report.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Performance Test Report - $TIMESTAMP</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .metrics { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background-color: #e8f4f8; padding: 15px; border-radius: 5px; flex: 1; }
        .passed { color: green; }
        .warning { color: orange; }
        .failed { color: red; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Performance Test Report</h1>
        <p><strong>Timestamp:</strong> $TIMESTAMP</p>
        <p><strong>Environment:</strong> $ENVIRONMENT</p>
        <p><strong>Test Type:</strong> $TEST_TYPE</p>
    </div>
    
    <h2>Test Results</h2>
    <div id="results">
        <p>Loading results...</p>
    </div>
    
    <h2>Performance Metrics</h2>
    <div class="metrics" id="metrics">
        <div class="metric">
            <h3>Response Time</h3>
            <p id="response-time">Loading...</p>
        </div>
        <div class="metric">
            <h3>Throughput</h3>
            <p id="throughput">Loading...</p>
        </div>
        <div class="metric">
            <h3>Error Rate</h3>
            <p id="error-rate">Loading...</p>
        </div>
    </div>
    
    <h2>Recommendations</h2>
    <div id="recommendations">
        <p>Loading...</p>
    </div>
    
    <script>
        // Load and display analysis results
        fetch('./performance-analysis.json')
            .then(response => response.json())
            .then(data => {
                document.getElementById('results').innerHTML = 
                    '<p class="' + data.status + '">Status: ' + data.status.toUpperCase() + '</p>' +
                    '<p>Total Tests: ' + data.totalTests + '</p>' +
                    '<p>Failed Tests: ' + data.failedTests + '</p>';
                
                document.getElementById('response-time').textContent = 
                    (data.performanceMetrics.responseTime || 'N/A') + 'ms';
                document.getElementById('throughput').textContent = 
                    (data.performanceMetrics.throughput || 'N/A') + ' req/s';
                document.getElementById('error-rate').textContent = 
                    (data.performanceMetrics.errorRate || 'N/A') + '%';
                
                const recommendations = data.recommendations.length > 0 
                    ? '<ul>' + data.recommendations.map(r => '<li>' + r + '</li>').join('') + '</ul>'
                    : '<p class="passed">No recommendations - performance is within acceptable limits</p>';
                document.getElementById('recommendations').innerHTML = recommendations;
            })
            .catch(error => {
                console.error('Error loading results:', error);
                document.getElementById('results').innerHTML = '<p class="failed">Error loading results</p>';
            });
    </script>
</body>
</html>
EOF
    
    log_success "Performance report generated: $report_file"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up..."
    
    # Stop test services if we started them
    pkill -f "mongod.*test" 2>/dev/null || true
    pkill -f "redis-server.*6379" 2>/dev/null || true
    
    # Clean up temporary files
    rm -f "$RESULTS_DIR/analyze.js" 2>/dev/null || true
    
    log_success "Cleanup completed"
}

# Main execution
main() {
    log_info "Starting performance testing CI/CD pipeline..."
    log_info "Environment: $ENVIRONMENT, Test Type: $TEST_TYPE"
    
    # Setup
    setup_environment
    install_dependencies
    build_application
    start_test_services
    
    local exit_code=0
    
    # Run tests based on type
    case "$TEST_TYPE" in
        "regression")
            run_regression_tests || exit_code=$?
            ;;
        "load")
            run_load_tests || exit_code=$?
            ;;
        "full")
            run_regression_tests || exit_code=$?
            if [ $exit_code -eq 0 ]; then
                run_load_tests || exit_code=$?
            fi
            ;;
        *)
            log_error "Unknown test type: $TEST_TYPE"
            exit_code=1
            ;;
    esac
    
    # Analyze results
    if [ $exit_code -eq 0 ]; then
        analyze_results || exit_code=$?
    fi
    
    # Generate report
    generate_report
    
    # Cleanup
    cleanup
    
    # Final status
    if [ $exit_code -eq 0 ]; then
        log_success "Performance testing completed successfully"
    else
        log_error "Performance testing failed with exit code $exit_code"
    fi
    
    exit $exit_code
}

# Handle script interruption
trap cleanup EXIT

# Run main function
main "$@"
